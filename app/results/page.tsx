"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Download, Share2, ArrowLeft } from "lucide-react"
import Link from "next/link"

interface GenerationResult {
  originalImage: string
  generatedImage: string
  timestamp: string
}

export default function ResultsPage() {
  const [result, setResult] = useState<GenerationResult | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Get the result from sessionStorage
    const storedResult = sessionStorage.getItem("generationResult")

    if (storedResult) {
      try {
        setResult(JSON.parse(storedResult))
      } catch (error) {
        console.error("Error parsing result:", error)
      }
    }

    setIsLoading(false)
  }, [])

  const handleDownload = async (imageUrl: string, filename: string) => {
    try {
      const response = await fetch(imageUrl)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = filename
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error("Error downloading image:", error)
    }
  }

  const handleDownloadSideBySide = async () => {
    if (!result) return
    try {
      // Load original image
      const img1: HTMLImageElement = document.createElement('img')
      img1.crossOrigin = 'anonymous'
      const p1 = new Promise<void>((resolve, reject) => {
        img1.onload = () => resolve()
        img1.onerror = () => reject()
        img1.src = result.originalImage
      })
      // Load generated image
      const img2: HTMLImageElement = document.createElement('img')
      img2.crossOrigin = 'anonymous'
      const p2 = new Promise<void>((resolve, reject) => {
        img2.onload = () => resolve()
        img2.onerror = () => reject()
        img2.src = result.generatedImage
      })
      await Promise.all([p1, p2])

      // Determine canvas size: match height, scale widths
      const height = Math.max(img1.height, img2.height)
      const width1 = (img1.width * height) / img1.height
      const width2 = (img2.width * height) / img2.height
      const canvas = document.createElement('canvas')
      canvas.width = width1 + width2
      canvas.height = height
      const ctx = canvas.getContext('2d')
      if (ctx) {
        ctx.drawImage(img1, 0, 0, width1, height)
        ctx.drawImage(img2, width1, 0, width2, height)

        canvas.toBlob((blob) => {
          if (!blob) {
            console.error('Canvas is empty')
            return
          }
          const url = URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = 'drawing-to-life-side-by-side.png'
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
          URL.revokeObjectURL(url)
        })
      }
    } catch (error) {
      console.error('Error combining images:', error)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-600"></div>
      </div>
    )
  }

  if (!result) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-4">
        <h1 className="text-2xl font-bold mb-4">No Result Found</h1>
        <p className="text-gray-600 mb-6">We couldn't find any transformation results.</p>
        <Link href="/">
          <Button className="bg-purple-600 hover:bg-purple-700">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Home
          </Button>
        </Link>
      </div>
    )
  }

  return (
    <main className="min-h-screen py-12 px-4 sm:px-6 lg:px-8">
      <div className="container mx-auto max-w-6xl">
        <div className="mb-8">
          <Link href="/">
            <Button variant="ghost" className="text-purple-600 hover:text-purple-700">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Button>
          </Link>
        </div>

        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold text-gray-900 sm:text-4xl">Your Drawing Transformed!</h1>
          <p className="mt-4 text-lg text-gray-600">
            Here's your child's drawing brought to life with our AI technology.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          <div className="bg-white rounded-xl overflow-hidden shadow-md">
            <div className="p-4 bg-gray-50 border-b">
              <h2 className="text-lg font-medium">Original Drawing</h2>
            </div>
            <div className="relative h-[400px] bg-gray-100">
              <Image
                src={result.originalImage || "/placeholder.svg"}
                alt="Original drawing"
                fill
                className="object-contain"
              />
            </div>
          </div>

          <div className="bg-white rounded-xl overflow-hidden shadow-md">
            <div className="p-4 bg-gray-50 border-b">
              <h2 className="text-lg font-medium">AI Transformation</h2>
            </div>
            <div className="relative h-[400px] bg-gray-100">
              <Image
                src={result.generatedImage || "/placeholder.svg"}
                alt="AI transformed image"
                fill
                className="object-contain"
              />
            </div>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row justify-center gap-4 mb-12">
          <Button
            onClick={() => handleDownload(result.generatedImage, "drawing-to-life-transformation.png")}
            className="bg-purple-600 hover:bg-purple-700"
          >
            <Download className="mr-2 h-4 w-4" />
            Download Image
          </Button>
          <Button
            onClick={handleDownloadSideBySide}
            className="bg-purple-600 hover:bg-purple-700"
          >
            <Download className="mr-2 h-4 w-4" />
            Download Side-by-Side
          </Button>
          <Button variant="outline">
            <Share2 className="mr-2 h-4 w-4" />
            Share
          </Button>
          <Link href="/">
            <Button variant="outline">Transform Another Drawing</Button>
          </Link>
        </div>

        <div className="text-center">
          <p className="text-sm text-gray-500">
            Created on {new Date(result.timestamp).toLocaleDateString()} at{" "}
            {new Date(result.timestamp).toLocaleTimeString()}
          </p>
        </div>
      </div>
    </main>
  )
}
