"use client"

import { useState } from "react"
import Image from "next/image"
import { motion } from "framer-motion"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { Button } from "@/components/ui/button"

type ExamplePair = {
  id: number
  drawing: string
  result: string
  alt: string
}

export default function ExampleGallery() {
  const examples: ExamplePair[] = [
    {
      id: 1,
      drawing:
        "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/494889704_17956792436945574_1932805159863557725_n.jpg-VyqZV5Vfie8HBahju3PXcajtjE0hgJ.jpeg",
      result:
        "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/494761147_17956792445945574_6536342402086905194_n.jpg-VIfigC4LYEBX3xmqQs3eLRh5tcflZJ.jpeg",
      alt: "Child's drawing transformed into a crystal creature",
    },
    {
      id: 2,
      drawing:
        "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/495446211_17956792418945574_942156344552347028_n.jpg-ZTK1iuUIXqfV6zreAEA2Bx0pLkBOnx.jpeg",
      result:
        "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/494722179_17956792427945574_7120378109307862919_n.jpg-A8DGO4IFSCMn5rryRu1ozbh1kELzR1.jpeg",
      alt: "Child's drawing transformed into a log creature",
    },
    {
      id: 3,
      drawing:
        "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/495026103_17956792364945574_2224655202871929804_n.jpg-UPQ7UdwfsP4IapCg8IEvDqkvdhYrsG.jpeg",
      result:
        "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/494895301_17956792373945574_5920433081264241765_n.jpg-AA0DezHHaeMpv3pZDeI4W7ImQCfEoW.jpeg",
      alt: "Child's drawing transformed into a furry red creature",
    },
    {
      id: 4,
      drawing:
        "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/495078403_17956792382945574_1567380377598989727_n.jpg-M4iFc6OlkNXp24qsMr03dryx16Tvel.jpeg",
      result:
        "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/494956115_17956792409945574_6941321807177962587_n.jpg-3vcISPiwsMchZxamS2ldyVVX1C4EhU.jpeg",
      alt: "Child's drawing transformed into a creature with pink belly",
    },
    {
      id: 5,
      drawing:
        "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/495351456_17956792400945574_697172967289052507_n.jpg-DgBE1QShqTH4TrgLX3CKCWZWTJ7jLU.jpeg",
      result:
        "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/495214194_17956792391945574_414145518409580409_n.jpg-xpFqhrB3NjddH9Beopdi6nzx2XtoIi.jpeg",
      alt: "Child's drawing transformed into a hairy creature",
    },
  ]

  const [currentIndex, setCurrentIndex] = useState(0)

  const nextExample = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % examples.length)
  }

  const prevExample = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + examples.length) % examples.length)
  }

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8">
      <div className="container mx-auto max-w-7xl">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">See the Magic in Action</h2>
          <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
            Browse our gallery of children's drawings and their photorealistic transformations. We preserve every detail
            and quirk that makes their art special.
          </p>
        </div>

        <div className="relative max-w-5xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div className="relative h-[400px] bg-gray-100 rounded-xl overflow-hidden shadow-md">
              <motion.div
                key={`drawing-${examples[currentIndex].id}`}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
                className="absolute inset-0 flex items-center justify-center p-4"
              >
                <Image
                  src={examples[currentIndex].drawing || "/placeholder.svg"}
                  alt={`Original drawing - ${examples[currentIndex].alt}`}
                  fill
                  className="object-contain"
                />
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-2 text-center">
                  Original Drawing
                </div>
              </motion.div>
            </div>

            <div className="relative h-[400px] bg-gray-100 rounded-xl overflow-hidden shadow-md">
              <motion.div
                key={`result-${examples[currentIndex].id}`}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
                className="absolute inset-0 flex items-center justify-center"
              >
                <Image
                  src={examples[currentIndex].result || "/placeholder.svg"}
                  alt={`Transformed result - ${examples[currentIndex].alt}`}
                  fill
                  className="object-cover"
                />
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-2 text-center">
                  AI Transformation
                </div>
              </motion.div>
            </div>
          </div>

          <Button
            variant="outline"
            size="icon"
            className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 bg-white rounded-full shadow-lg z-10 hidden md:flex"
            onClick={prevExample}
          >
            <ChevronLeft className="h-6 w-6" />
            <span className="sr-only">Previous example</span>
          </Button>

          <Button
            variant="outline"
            size="icon"
            className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 bg-white rounded-full shadow-lg z-10 hidden md:flex"
            onClick={nextExample}
          >
            <ChevronRight className="h-6 w-6" />
            <span className="sr-only">Next example</span>
          </Button>

          <div className="flex justify-center mt-8 gap-2">
            {examples.map((_, index) => (
              <button
                key={index}
                className={`h-3 w-3 rounded-full ${currentIndex === index ? "bg-purple-600" : "bg-gray-300"}`}
                onClick={() => setCurrentIndex(index)}
                aria-label={`Go to example ${index + 1}`}
              />
            ))}
          </div>

          <div className="flex justify-center mt-8 md:hidden gap-4">
            <Button variant="outline" onClick={prevExample}>
              <ChevronLeft className="h-5 w-5 mr-2" />
              Previous
            </Button>
            <Button variant="outline" onClick={nextExample}>
              Next
              <ChevronRight className="h-5 w-5 ml-2" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
