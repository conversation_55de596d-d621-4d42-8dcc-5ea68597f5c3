import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import UploadArea from "@/components/upload-area"
import ExampleGallery from "@/components/example-gallery"
import HowItWorks from "@/components/how-it-works"
import FAQSection from "@/components/faq-section"

// Define example data directly or import from a shared location
const examples = [
  {
    id: 1,
    result: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/494761147_17956792445945574_6536342402086905194_n.jpg-VIfigC4LYEBX3xmqQs3eLRh5tcflZJ.jpeg",
    alt: "Child's drawing transformed into a crystal creature",
  },
  {
    id: 2,
    result: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/494722179_17956792427945574_7120378109307862919_n.jpg-A8DGO4IFSCMn5rryRu1ozbh1kELzR1.jpeg",
    alt: "Child's drawing transformed into a log creature",
  },
  {
    id: 3,
    result: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/494895301_17956792373945574_5920433081264241765_n.jpg-AA0DezHHaeMpv3pZDeI4W7ImQCfEoW.jpeg",
    alt: "Child's drawing transformed into a furry red creature",
  },
  {
    id: 4,
    result: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/494956115_17956792409945574_6941321807177962587_n.jpg-3vcISPiwsMchZxamS2ldyVVX1C4EhU.jpeg",
    alt: "Child's drawing transformed into a creature with pink belly",
  },
]

export default function Home() {
  return (
    <main className="flex min-h-screen flex-col">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-b from-purple-50 to-white py-10 px-4 sm:px-6 lg:px-8 text-center">
        <h1 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl md:text-5xl mb-2">
          <span className="block">Bring Your Children's</span>
          <span className="block text-purple-600">Drawing to Life</span>
        </h1>
        <p className="text-md text-gray-600 max-w-2xl mx-auto">
          Upload your child's drawing below and watch as our AI transforms it into a photorealistic masterpiece.
        </p>
      </section>

      {/* Central Upload Area with Examples Around */}
      <section className="flex-grow flex items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="container mx-auto max-w-7xl flex flex-col lg:flex-row items-center justify-center gap-8">
          {/* Left Examples */}
          <div className="hidden lg:flex flex-col gap-4">
            <div className="relative w-32 h-32 bg-gray-100 rounded-lg overflow-hidden shadow-md">
              <Image src={examples[0].result} alt={examples[0].alt} fill className="object-cover" />
            </div>
            <div className="relative w-32 h-32 bg-gray-100 rounded-lg overflow-hidden shadow-md">
              <Image src={examples[1].result} alt={examples[1].alt} fill className="object-cover" />
            </div>
          </div>

          {/* Central Upload Area */}
          <div className="w-full max-w-2xl">
            <UploadArea />
          </div>

          {/* Right Examples */}
          <div className="hidden lg:flex flex-col gap-4">
             <div className="relative w-32 h-32 bg-gray-100 rounded-lg overflow-hidden shadow-md">
               <Image src={examples[2].result} alt={examples[2].alt} fill className="object-cover" />
             </div>
             <div className="relative w-32 h-32 bg-gray-100 rounded-lg overflow-hidden shadow-md">
               <Image src={examples[3].result} alt={examples[3].alt} fill className="object-cover" />
             </div>
          </div>

           {/* Examples for smaller screens */}
           <div className="lg:hidden grid grid-cols-2 sm:grid-cols-4 gap-4 mt-8">
             <div className="relative w-full aspect-square bg-gray-100 rounded-lg overflow-hidden shadow-md">
               <Image src={examples[0].result} alt={examples[0].alt} fill className="object-cover" />
             </div>
             <div className="relative w-full aspect-square bg-gray-100 rounded-lg overflow-hidden shadow-md">
               <Image src={examples[1].result} alt={examples[1].alt} fill className="object-cover" />
             </div>
             <div className="relative w-full aspect-square bg-gray-100 rounded-lg overflow-hidden shadow-md">
               <Image src={examples[2].result} alt={examples[2].alt} fill className="object-cover" />
             </div>
             <div className="relative w-full aspect-square bg-gray-100 rounded-lg overflow-hidden shadow-md">
               <Image src={examples[3].result} alt={examples[3].alt} fill className="object-cover" />
             </div>
           </div>
        </div>
      </section>

      {/* Example Gallery Section - REMOVED */}
      {/* <ExampleGallery /> */}

      {/* Upload Section - REMOVED */}
      {/* 
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="container mx-auto max-w-5xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">Upload Your Child's Drawing</h2>
            <p className="mt-4 text-lg text-gray-600">
              Drag and drop your image below, and watch as our AI transforms it into a photorealistic masterpiece.
            </p>
          </div>
          <UploadArea /> 
        </div>
      </section>
      */}

      {/* How It Works Section - REMOVED */}
      {/* <HowItWorks /> */}

      {/* Testimonials or Additional Info - REMOVED */}
      {/* 
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto max-w-7xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">Preserve Their Imagination Forever</h2>
            <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
              Every scribble, every wobbly line, every burst of creativity - transformed but never "fixed" or
              "corrected." We keep the soul of your child's creation intact.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold mb-3">Preserve Authenticity</h3>
              <p className="text-gray-600">
                We maintain all the unique characteristics of the original drawing, including proportions and
                "imperfections".
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold mb-3">Realistic Rendering</h3>
              <p className="text-gray-600">
                Advanced AI transforms simple drawings into photorealistic images with appropriate textures and
                lighting.
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold mb-3">Easy to Download</h3>
              <p className="text-gray-600">
                Download your transformed images instantly and share them with friends and family.
              </p>
            </div>
          </div>
        </div>
      </section>
      */}

      {/* FAQ Section - REMOVED */}
      {/* <FAQSection /> */}

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto max-w-7xl">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-2xl font-bold mb-4">Drawing to Life</h3>
              <p className="text-gray-400 max-w-md">
                Transforming children's imagination into photorealistic art while preserving the unique character of
                their creations.
              </p>
            </div>
            <div className="grid grid-cols-2 gap-8">
              <div>
                <h4 className="text-lg font-semibold mb-4">Links</h4>
                <ul className="space-y-2">
                  <li>
                    <a href="#" className="text-gray-400 hover:text-white transition">
                      Home
                    </a>
                  </li>
                  <li>
                    <a href="#" className="text-gray-400 hover:text-white transition">
                      Gallery
                    </a>
                  </li>
                  <li>
                    <a href="#" className="text-gray-400 hover:text-white transition">
                      How It Works
                    </a>
                  </li>
                  <li>
                    <a href="#" className="text-gray-400 hover:text-white transition">
                      Contact
                    </a>
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="text-lg font-semibold mb-4">Legal</h4>
                <ul className="space-y-2">
                  <li>
                    <a href="#" className="text-gray-400 hover:text-white transition">
                      Privacy Policy
                    </a>
                  </li>
                  <li>
                    <a href="#" className="text-gray-400 hover:text-white transition">
                      Terms of Service
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400">
            <p>&copy; {new Date().getFullYear()} Drawing to Life. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </main>
  )
}
