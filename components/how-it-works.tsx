import { Sparkles, Upload, ImageIcon, Download } from "lucide-react"

export default function HowItWorks() {
  const steps = [
    {
      icon: <Upload className="h-10 w-10 text-purple-600" />,
      title: "Upload Drawing",
      description: "Upload your child's drawing through our simple drag-and-drop interface.",
    },
    {
      icon: <Sparkles className="h-10 w-10 text-purple-600" />,
      title: "AI Processing",
      description:
        "Our AI processes the drawing using a consistent system prompt that preserves all the unique characteristics.",
    },
    {
      icon: <ImageIcon className="h-10 w-10 text-purple-600" />,
      title: "See Results",
      description:
        "Watch as the drawing is transformed into a photorealistic image while maintaining its original charm.",
    },
    {
      icon: <Download className="h-10 w-10 text-purple-600" />,
      title: "Download & Share",
      description: "Download the transformed image and share it with friends and family.",
    },
  ]

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="container mx-auto max-w-7xl">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">How It Works</h2>
          <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
            Our simple process transforms children's drawings into photorealistic images in just a few steps.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {steps.map((step, index) => (
            <div key={index} className="flex flex-col items-center text-center">
              <div className="relative">
                <div className="flex items-center justify-center h-20 w-20 rounded-full bg-purple-100 mb-4">
                  {step.icon}
                </div>
                {index < steps.length - 1 && (
                  <div className="hidden lg:block absolute top-10 left-full w-full h-0.5 bg-purple-200 -z-10 transform -translate-x-8" />
                )}
              </div>
              <h3 className="text-xl font-semibold mb-2">{step.title}</h3>
              <p className="text-gray-600">{step.description}</p>
            </div>
          ))}
        </div>

        <div className="mt-16 text-center">
          <div className="inline-block p-6 bg-purple-50 rounded-lg border border-purple-100">
            <h3 className="text-xl font-semibold mb-3">Our AI Prompt</h3>
            <p className="text-gray-600 text-sm text-left max-w-3xl">
              "Take this drawing created by my child and transform it into a photorealistic image or realistic 3D
              render. I don't know what it's supposed to be - it could be a creature, object, or something completely
              from their imagination. Keep the original shape, proportions, line lengths, and all imperfections exactly
              as they are in the drawing - including any slanted eyes, uneven lines, or strange markings. Do not
              correct, smooth out, or change any details of their design..."
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}
