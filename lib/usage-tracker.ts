// Simple utility to track daily usage of the app

// Define the structure of our usage data
interface UsageData {
  count: number
  lastReset: string // ISO date string
}

// Check if a new day has started (UTC)
const isNewDay = (lastResetDate: string): boolean => {
  const lastReset = new Date(lastResetDate)
  const now = new Date()

  return (
    lastReset.getUTCFullYear() !== now.getUTCFullYear() ||
    lastReset.getUTCMonth() !== now.getUTCMonth() ||
    lastReset.getUTCDate() !== now.getUTCDate()
  )
}

// Get current usage data
export const getUsageData = (): UsageData => {
  // Default to empty usage data
  const defaultData: UsageData = {
    count: 0,
    lastReset: new Date().toISOString(),
  }

  // Try to get data from localStorage
  try {
    const storedData = localStorage.getItem("drawingToLifeUsage")

    if (!storedData) {
      return defaultData
    }

    const parsedData: UsageData = JSON.parse(storedData)

    // Check if we need to reset for a new day
    if (isNewDay(parsedData.lastReset)) {
      const resetData = {
        count: 0,
        lastReset: new Date().toISOString(),
      }

      // Save the reset data
      localStorage.setItem("drawingToLifeUsage", JSON.stringify(resetData))
      return resetData
    }

    return parsedData
  } catch (error) {
    // If there's any error, return default data
    console.error("Error retrieving usage data:", error)
    return defaultData
  }
}

// Record a new generation
export const recordGeneration = (): UsageData => {
  const currentData = getUsageData()

  const updatedData: UsageData = {
    ...currentData,
    count: currentData.count + 1,
  }

  // Save the updated data
  localStorage.setItem("drawingToLifeUsage", JSON.stringify(updatedData))

  return updatedData
}

// Check if user has reached daily limit
export const hasReachedDailyLimit = (): boolean => {
  const { count } = getUsageData()
  const DAILY_LIMIT = 2

  return count >= DAILY_LIMIT
}

// Get remaining generations
export const getRemainingGenerations = (): number => {
  const { count } = getUsageData()
  const DAILY_LIMIT = 2

  return Math.max(0, DAILY_LIMIT - count)
}
