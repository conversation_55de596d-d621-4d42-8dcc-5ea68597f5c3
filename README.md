# Drawing to Life

"Drawing to Life" is a web application that transforms children's drawings into photorealistic masterpieces using AI. Users can upload a drawing, and the application will generate an imaginative, high-quality image based on the original artwork, preserving its unique charm.

## Tech Stack

*   **Framework**: Next.js (App Router)
*   **Language**: TypeScript
*   **Styling**: Tailwind CSS
*   **UI Components**: Radix UI, Shadcn/UI (inferred from `components.json` and Radix dependencies)
*   **Icons**: Lucide React
*   **Animation**: Framer Motion
*   **Form Handling**: React Hook Form
*   **Validation**: Zod
*   **AI**: OpenAI API (inferred from dependencies and functionality)
*   **Image Handling**: Next/Image, potentially `canvas` for client-side operations.
*   **Real-time**: `ws` (WebSocket) dependency suggests potential real-time features.
*   **Toasts/Notifications**: Sonner
*   **Charts**: Recharts (though usage isn't immediately visible on the main page)

## Getting Started

### Prerequisites

*   Node.js (v18.x or later recommended)
*   npm, pnpm, or yarn

### Installation

1.  Clone the repository:
    ```bash
    git clone <repository-url>
    cd my-v0-project 
    ```
    (Note: The project directory name is `my-v0-project` as per `package.json`)

2.  Install dependencies. Choose one of the following based on your preferred package manager:
    *   Using npm:
        ```bash
        npm install
        ```
    *   Using pnpm:
        ```bash
        pnpm install
        ```
    *   Using yarn:
        ```bash
        yarn install
        ```

3.  Set up environment variables:
    Create a `.env.local` file in the root of the project and add any necessary environment variables (e.g., OpenAI API key).
    ```
    OPENAI_API_KEY=your_openai_api_key_here
    # Add other variables if needed
    ```

### Running the Development Server

Execute the following command:

*   Using npm:
    ```bash
    npm run dev
    ```
*   Using pnpm:
    ```bash
    pnpm dev
    ```
*   Using yarn:
    ```bash
    yarn dev
    ```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the application.

## Available Scripts

In the `package.json` file, you can find the following scripts:

*   `dev`: Runs the application in development mode.
*   `build`: Creates a production build of the application.
*   `start`: Starts the production server.
*   `lint`: Lints the codebase using Next.js's built-in ESLint configuration.

## Project Structure

Here's a brief overview of the key directories:

*   `app/`: Contains the core application code, using the Next.js App Router.
    *   `app/page.tsx`: The main landing page.
    *   `app/layout.tsx`: The main layout component.
    *   `app/api/generate/route.ts`: The API endpoint for handling image generation requests.
    *   `app/results/page.tsx`: The page to display the generated image.
    *   `app/globals.css`: Global stylesheets.
*   `components/`: Contains reusable React components used throughout the application.
    *   `components/ui/`: Likely contains UI primitives from Shadcn/UI.
    *   `components/upload-area.tsx`: Component for handling file uploads.
    *   `components/example-gallery.tsx`: Component for displaying example transformations.
*   `public/`: Contains static assets like images and fonts.
*   `lib/`: Likely contains utility functions and helper modules (standard Next.js practice).
*   `hooks/`: Contains custom React hooks.
*   `styles/`: May contain additional global or component-specific styles.

## How It Works

1.  The user visits the main page.
2.  They upload a child's drawing using the `UploadArea` component.
3.  The frontend sends the image to the `/api/generate` backend endpoint.
4.  The backend API (likely using OpenAI) processes the drawing and generates a photorealistic version.
5.  The user is redirected to or shown the result on the `app/results/page.tsx` page.

---

This README provides a starting point. Feel free to expand it with more details about deployment, specific AI model integration, contribution guidelines, or other relevant information. 