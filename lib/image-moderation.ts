// Image moderation utilities to verify uploads are drawings

import { createCanvas, loadImage } from "canvas"

// Interface for moderation result
export interface ModerationResult {
  isDrawing: boolean
  confidence: number
  message: string
}

/**
 * Analyzes an image to determine if it's likely a drawing/sketch
 * This uses several heuristics to distinguish drawings from photographs
 * with reduced strictness to accept more legitimate children's drawings
 */
export async function analyzeImage(imageDataUrl: string): Promise<ModerationResult> {
  try {
    // Load the image
    const image = await loadImage(imageDataUrl)

    // Create a canvas to analyze the image
    const canvas = createCanvas(image.width, image.height)
    const ctx = canvas.getContext("2d")
    ctx.drawImage(image, 0, 0)

    // Get image data for analysis
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
    const data = imageData.data

    // Check if there's a significant white/light area (paper)
    const paperScore = detectPaperArea(data)

    // Analyze edge density (drawings typically have more distinct edges)
    const edgeScore = calculateEdgeDensity(data, canvas.width, canvas.height)

    // Analyze color distribution (drawings often have fewer unique colors)
    const colorScore = analyzeColorDistribution(data)

    // Analyze texture complexity (photos usually have more complex textures)
    const textureScore = analyzeTextureComplexity(data, canvas.width, canvas.height)

    // Calculate final confidence score (0-1)
    // Higher score = more likely to be a drawing
    const drawingConfidence = calculateDrawingConfidence(paperScore, edgeScore, colorScore, textureScore)

    // Determine if it's a drawing based on confidence threshold
    // Lowered threshold from 0.65 to 0.45 to be less strict
    const isDrawing = drawingConfidence > 0.45

    return {
      isDrawing,
      confidence: drawingConfidence,
      message: isDrawing
        ? "Image appears to be a drawing."
        : "This doesn't look like a hand-drawn image. Please upload a child's drawing.",
    }
  } catch (error) {
    console.error("Error analyzing image:", error)
    // If there's an error in analysis, default to accepting the image
    return {
      isDrawing: true,
      confidence: 0.5,
      message: "Unable to analyze image fully, proceeding with generation.",
    }
  }
}

/**
 * Detects if there's a significant paper/light area in the image
 * This helps identify photos of drawings on paper
 */
function detectPaperArea(data: Uint8ClampedArray): number {
  let lightPixels = 0
  const totalPixels = data.length / 4

  // Count light-colored pixels (likely paper)
  for (let i = 0; i < data.length; i += 4) {
    const r = data[i]
    const g = data[i + 1]
    const b = data[i + 2]

    // Calculate brightness (simple average)
    const brightness = (r + g + b) / 3

    // If pixel is light (paper-like)
    if (brightness > 200) {
      lightPixels++
    }
  }

  // Calculate percentage of light pixels
  const lightPercentage = lightPixels / totalPixels

  // Score based on light area percentage
  // Higher score if there's a significant paper-like area
  return Math.min(1, lightPercentage * 2)
}

/**
 * Calculates edge density in the image
 * Drawings typically have more distinct edges and lines
 */
function calculateEdgeDensity(data: Uint8ClampedArray, width: number, height: number): number {
  let edgeCount = 0
  const threshold = 25 // Reduced threshold for edge detection (was 30)

  // Simple edge detection by comparing adjacent pixels
  for (let y = 1; y < height - 1; y++) {
    for (let x = 1; x < width - 1; x++) {
      const idx = (y * width + x) * 4
      const idxRight = (y * width + (x + 1)) * 4
      const idxDown = ((y + 1) * width + x) * 4

      // Calculate differences with adjacent pixels
      const diffX =
        Math.abs(data[idx] - data[idxRight]) +
        Math.abs(data[idx + 1] - data[idxRight + 1]) +
        Math.abs(data[idx + 2] - data[idxRight + 2])

      const diffY =
        Math.abs(data[idx] - data[idxDown]) +
        Math.abs(data[idx + 1] - data[idxDown + 1]) +
        Math.abs(data[idx + 2] - data[idxDown + 2])

      // If the difference is above threshold, count as an edge
      if (diffX > threshold || diffY > threshold) {
        edgeCount++
      }
    }
  }

  // Normalize edge count by image size
  return edgeCount / (width * height)
}

/**
 * Analyzes color distribution
 * Drawings often have fewer unique colors and more solid areas
 */
function analyzeColorDistribution(data: Uint8ClampedArray): number {
  const colorMap = new Map<string, number>()
  const sampleSize = Math.min(data.length / 4, 10000) // Limit sample size for performance
  const step = Math.max(1, Math.floor(data.length / 4 / sampleSize))

  // Sample colors from the image
  for (let i = 0; i < data.length; i += 4 * step) {
    // Create a color key with reduced precision to group similar colors
    // Increased color grouping to be more lenient
    const r = Math.floor(data[i] / 15) * 15
    const g = Math.floor(data[i + 1] / 15) * 15
    const b = Math.floor(data[i + 2] / 15) * 15
    const colorKey = `${r},${g},${b}`

    colorMap.set(colorKey, (colorMap.get(colorKey) || 0) + 1)
  }

  // Calculate color diversity score
  // Fewer unique colors = higher score (more likely to be a drawing)
  const uniqueColors = colorMap.size
  const maxColors = 600 // Increased from 500 to be more lenient

  return Math.max(0, Math.min(1, 1 - uniqueColors / maxColors))
}

/**
 * Analyzes texture complexity
 * Photos usually have more complex textures than drawings
 */
function analyzeTextureComplexity(data: Uint8ClampedArray, width: number, height: number): number {
  // Calculate local variance in small regions
  const blockSize = 8
  let totalVariance = 0
  let blockCount = 0

  for (let y = 0; y < height - blockSize; y += blockSize) {
    for (let x = 0; x < width - blockSize; x += blockSize) {
      let sum = 0
      let sumSq = 0
      let count = 0

      // Calculate variance in this block
      for (let by = 0; by < blockSize; by++) {
        for (let bx = 0; bx < blockSize; bx++) {
          const idx = ((y + by) * width + (x + bx)) * 4
          // Use grayscale value
          const gray = (data[idx] + data[idx + 1] + data[idx + 2]) / 3
          sum += gray
          sumSq += gray * gray
          count++
        }
      }

      const mean = sum / count
      const variance = sumSq / count - mean * mean
      totalVariance += variance
      blockCount++
    }
  }

  const avgVariance = totalVariance / blockCount
  // Lower variance = more likely to be a drawing
  // Normalize to 0-1 range with higher tolerance
  return Math.max(0, Math.min(1, 1 - avgVariance / 2500)) // Increased from 2000
}

/**
 * Calculate final confidence score that the image is a drawing
 */
function calculateDrawingConfidence(
  paperScore: number,
  edgeScore: number,
  colorScore: number,
  textureScore: number,
): number {
  // Weighted combination of scores
  // Added paper detection and adjusted weights to be more lenient
  return paperScore * 0.35 + edgeScore * 0.3 + colorScore * 0.2 + textureScore * 0.15
}

/**
 * Server-side moderation function that combines multiple checks
 */
export async function moderateImage(imageFile: File): Promise<ModerationResult> {
  return new Promise((resolve, reject) => {
    try {
      const reader = new FileReader()

      reader.onload = async (e) => {
        const dataUrl = e.target?.result as string

        // Analyze if it's a drawing
        const drawingAnalysis = await analyzeImage(dataUrl)

        resolve(drawingAnalysis)
      }

      reader.onerror = () => {
        // If there's an error reading the file, default to accepting it
        resolve({
          isDrawing: true,
          confidence: 0.5,
          message: "Unable to analyze image, proceeding with generation.",
        })
      }

      reader.readAsDataURL(imageFile)
    } catch (error) {
      // If there's an error in the moderation process, default to accepting
      resolve({
        isDrawing: true,
        confidence: 0.5,
        message: "Error during moderation, proceeding with generation.",
      })
    }
  })
}
