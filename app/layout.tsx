import type React from "react"
import "./globals.css"
import { Inter } from "next/font/google"
import { ThemeProvider } from "@/components/theme-provider"

const inter = Inter({ subsets: ["latin"] })

export const metadata = {
  title: "Drawing to Life - Transform Children's Drawings into Photorealistic Images",
  description:
    "Upload your child's drawing and watch as our AI transforms it into a photorealistic image while preserving all the unique characteristics of their original artwork.",
    generator: 'v0.dev',
    developers: [{'name': '<PERSON>', 'email': 'jeremyash<PERSON>@gmail.com'}]
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning={true}>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="light">
          {children}
        </ThemeProvider>
      </body>
    </html>
  )
}
