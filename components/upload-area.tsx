"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import Image from "next/image"
import { Upload, X, ImageIcon, AlertCircle, Info, CheckCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { recordGeneration, hasReachedDailyLimit, getRemainingGenerations } from "@/lib/usage-tracker"
import { moderateImage } from "@/lib/image-moderation"
import { useRouter } from "next/navigation"

export default function UploadArea() {
  const router = useRouter()
  const [isDragging, setIsDragging] = useState(false)
  const [file, setFile] = useState<File | null>(null)
  const [preview, setPreview] = useState<string | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [remainingGenerations, setRemainingGenerations] = useState(2)
  const [isLimitReached, setIsLimitReached] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [moderationWarning, setModerationWarning] = useState<string | null>(null)
  const [moderationConfidence, setModerationConfidence] = useState<number | null>(null)
  const [overrideModeration, setOverrideModeration] = useState(false)

  const fileInputRef = useRef<HTMLInputElement>(null)

  // Initialize usage data from localStorage
  useEffect(() => {
    // Only run on client side
    /* // --- LIMIT REMOVED ---
    if (typeof window !== "undefined") {
      const remaining = getRemainingGenerations()
      setRemainingGenerations(remaining)
      setIsLimitReached(hasReachedDailyLimit())
    }
    */ // --- LIMIT REMOVED ---
  }, [])

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = () => {
    setIsDragging(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0])
    }
  }

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0])
    }
  }

  const handleFile = async (file: File) => {
    // Check if file is an image
    if (!file.type.match("image.*")) {
      setError("Please upload an image file")
      return
    }

    setFile(file)
    setError(null)
    setModerationWarning(null)
    setModerationConfidence(null)
    setOverrideModeration(false)

    // Generate preview using object URL to avoid large base64 strings
    const objectUrl = URL.createObjectURL(file)
    setPreview(objectUrl)
    try {
      // Perform client-side moderation check
      const moderationResult = await moderateImage(file)

      if (!moderationResult.isDrawing) {
        setModerationWarning(moderationResult.message)
        setModerationConfidence(moderationResult.confidence)
      }
    } catch (err) {
      console.error("Error during image moderation:", err)
    }
  }

  const clearFile = () => {
    // Revoke object URL to free memory
    if (preview && preview.startsWith("blob:")) {
      URL.revokeObjectURL(preview)
    }
    setFile(null)
    setPreview(null)
    setError(null)
    setModerationWarning(null)
    setModerationConfidence(null)
    setOverrideModeration(false)
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  const toggleOverride = () => {
    setOverrideModeration(!overrideModeration)
  }

  const handleSubmit = async () => {
    /* // --- LIMIT REMOVED ---
    if (isLimitReached) {
      alert("You've reached your daily limit of 2 transformations. Please try again tomorrow.")
      return
    }
    */ // --- LIMIT REMOVED ---

    if (!file) {
      setError("Please select an image file")
      return
    }

    try {
      setIsUploading(true)
      setUploadProgress(10)
      setError(null)

      // Create form data to send to the API
      const formData = new FormData()
      formData.append("image", file)

      // Skip moderation if user has chosen to override or if confidence is borderline
      if (overrideModeration || (moderationConfidence !== null && moderationConfidence > 0.3)) {
        formData.append("skipModeration", "true")
      }

      // Simulate progress while waiting for API response
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return 90
          }
          return prev + 5
        })
      }, 500)

      // Send the request to our API
      const response = await fetch("/api/generate", {
        method: "POST",
        body: formData,
      })

      clearInterval(progressInterval)

      // First read the response as text
      const responseText = await response.text()

      // Then try to parse it as JSON
      let data
      try {
        data = JSON.parse(responseText)
      } catch (jsonError) {
        console.error("Error parsing JSON response:", jsonError)
        throw new Error(`Server returned invalid JSON. Response: ${responseText.substring(0, 100)}...`)
      }

      // Check if the response indicates an error
      if (!response.ok) {
        throw new Error(data.message || data.error || "Failed to generate image")
      }

      setUploadProgress(100)

      /* // --- LIMIT REMOVED ---
      // Record this generation
      recordGeneration()
      const remaining = getRemainingGenerations()
      setRemainingGenerations(remaining)
      setIsLimitReached(hasReachedDailyLimit())
      */ // --- LIMIT REMOVED ---

      // Store the result in sessionStorage for the results page
      sessionStorage.setItem(
        "generationResult",
        JSON.stringify({
          originalImage: preview,
          generatedImage: data.imageUrl,
          timestamp: new Date().toISOString(),
        }),
      )

      // Redirect to the results page
      router.push("/results")
    } catch (err) {
      console.error("Error generating image:", err)
      setError(err instanceof Error ? err.message : "An unexpected error occurred")
      setUploadProgress(0)
    } finally {
      setIsUploading(false)
    }
  }

  return (
    <div className="w-full">
      {/* // --- LIMIT REMOVED ---
      {isLimitReached && (
        <Alert className="mb-6 bg-amber-50 border-amber-200">
          <AlertCircle className="h-4 w-4 text-amber-600" />
          <AlertDescription className="text-amber-700">
            You've reached your daily limit of 2 transformations. Your limit will reset at midnight UTC.
          </AlertDescription>
        </Alert>
      )}
      */}

      {error && (
        <Alert className="mb-6 bg-red-50 border-red-200">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-700">{error}</AlertDescription>
        </Alert>
      )}

      {moderationWarning && !overrideModeration && (
        <Alert className="mb-6 bg-amber-50 border-amber-200">
          <Info className="h-4 w-4 text-amber-600" />
          <div className="flex flex-col w-full">
            <AlertDescription className="text-amber-700 mb-2">
              {moderationWarning} Our system works best with simple hand-drawn sketches.
            </AlertDescription>
            <div className="flex justify-end">
              <Button variant="outline" size="sm" onClick={toggleOverride} className="text-amber-700 border-amber-300">
                This is a drawing, continue anyway
              </Button>
            </div>
          </div>
        </Alert>
      )}

      {overrideModeration && (
        <Alert className="mb-6 bg-green-50 border-green-200">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-700">
            Moderation override enabled. Your image will be processed as a drawing.
          </AlertDescription>
        </Alert>
      )}

      {!file ? (
        <div
          className={`border-2 border-dashed rounded-xl p-8 text-center ${
            isDragging ? "border-purple-500 bg-purple-50" : "border-gray-300"
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="p-4 bg-purple-100 rounded-full">
              <Upload className="h-10 w-10 text-purple-600" />
            </div>
            <h3 className="text-lg font-medium">Drag and drop your image here</h3>
            <p className="text-sm text-gray-500">
              Upload a child's drawing (not photographs). Support for JPG, PNG files.
            </p>
            <div className="p-3 bg-blue-50 rounded-md w-full max-w-md">
              <p className="text-xs text-blue-700">
                <strong>Best results:</strong> Simple hand-drawn sketches on plain backgrounds. Photos of real people,
                toys, or other objects won't work well.
              </p>
            </div>
            <Button
              onClick={() => fileInputRef.current?.click()}
              className="bg-purple-600 hover:bg-purple-700"
              disabled={false}
            >
              Select File
            </Button>
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileInput}
              accept="image/png, image/jpeg"
              className="hidden"
              disabled={false}
            />
          </div>
        </div>
      ) : (
        <div className="border rounded-xl p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Ready to transform</h3>
            <Button variant="ghost" size="icon" onClick={clearFile}>
              <X className="h-5 w-5" />
            </Button>
          </div>

          <div className="relative h-[300px] bg-gray-100 rounded-lg overflow-hidden mb-4">
            {preview && (
              <Image
                src={preview || "/placeholder.svg"}
                alt="Preview of uploaded drawing"
                fill
                className="object-contain"
              />
            )}
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <ImageIcon className="h-5 w-5 text-gray-500" />
              <span className="text-sm text-gray-500">{file.name}</span>
            </div>

            {isUploading ? (
              <div className="w-full max-w-xs">
                <Progress value={uploadProgress} className="h-2" />
                <p className="text-xs text-gray-500 mt-1">Processing your image...</p>
              </div>
            ) : (
              <Button onClick={handleSubmit} className="bg-purple-600 hover:bg-purple-700" disabled={false}>
                Transform Now
              </Button>
            )}
          </div>
        </div>
      )}

      <div className="mt-6 text-center">
        <div className="text-sm text-gray-500">
          <p>
            By uploading, you agree to our{" "}
            <a href="#" className="text-purple-600 hover:underline">
              Terms of Service
            </a>{" "}
            and{" "}
            <a href="#" className="text-purple-600 hover:underline">
              Privacy Policy
            </a>
          </p>
        </div>
        <div className="mt-3 p-2 bg-purple-50 rounded-md inline-block">
          <p className="text-sm font-medium">
            <span className="text-purple-600">{remainingGenerations}</span> of{" "}
            <span className="text-purple-600">2</span> daily transformations remaining
          </p>
        </div>
      </div>
    </div>
  )
}
