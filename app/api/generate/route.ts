import { type NextRequest, NextResponse } from "next/server";
import OpenA<PERSON>, { toFile } from "openai";
import { analyzeImage } from "@/lib/image-moderation";
import sharp from 'sharp'; // Import sharp
import { File } from "formdata-node";

// Configure the API route to allow larger body sizes (e.g., for file uploads)
export const config = {
  api: {
    bodyParser: {
      sizeLimit: "25mb", // Set desired size limit here
    },
  },
};

// Initialize the OpenAI client with the API key from environment variables
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// The system prompt that will be used for all transformations
const SYSTEM_PROMPT = `
Take this drawing created by my child and transform it into a photorealistic image or realistic 3D render. I don't know what it's supposed to be it could be a creature, object, or something completely from their imagination. Keep the original shape, proportions, line lengths, and all imperfections exactly as they are in the drawing - including any slanted eyes, uneven lines, or strange markings. Do not correct, smooth out, or change any details of their design.
Make it look like this thing exists in the real world, with realistic textures (skin, fur, metal, etc.) and natural lighting. You can add realistic shadows and an environment or background that fits the feel of the drawing, but don't change anything about the form or details of what they created. No pencil crayon textures or hand-drawn styles this must look like a photo or CGI render, but staying true to their imagination.
`;

const MAX_SIZE_BYTES = 25 * 1024 * 1024; // 25MB gpt-image-1 limit

export async function POST(request: NextRequest) {
  try {
    // Parse the form data from the request
    const formData = await request.formData();
    const imageFile = formData.get("image") as File | null;
    const skipModerationFormData = formData.get("skipModeration") === "true";

    // Check for the secret bypass query parameter
    const bypassQueryParam =
      request.nextUrl.searchParams.get("bypass_moderation") === "true";

    // Determine if moderation should be skipped
    const shouldSkipModeration = skipModerationFormData || bypassQueryParam;

    // Validate the request
    if (!imageFile) {
      return NextResponse.json(
        { error: "No image file provided" },
        { status: 400 }
      );
    }

    // Validate file type (Allow PNG, JPEG, WEBP for gpt-image-1)
    const allowedTypes = ["image/png", "image/jpeg", "image/webp"];
    if (!allowedTypes.includes(imageFile.type)) {
      return NextResponse.json(
        {
          error: `Invalid file type. Allowed types are: ${allowedTypes.join(", ")}.`,
        },
        { status: 400 }
      );
    }

    // Removed original size validation here, will handle with resizing

    try {
      // Convert the file to a buffer first
      const arrayBuffer = await imageFile.arrayBuffer();
      // @ts-ignore - Buffer.from accepts ArrayBuffer, but TS types might be mismatched
      // Cast Buffer explicitly
      let imageBuffer = Buffer.from(arrayBuffer) as Buffer;
      let imageType = imageFile.type; // Keep track of type

      // Resize by resolution if image exceeds gpt-image-1 token limits (max 16384 tokens)
      const metadata = await sharp(imageBuffer).metadata();
      const MAX_DIMENSION = 1024;
      if ((metadata.width && metadata.width > MAX_DIMENSION) || (metadata.height && metadata.height > MAX_DIMENSION)) {
        console.log(`Image resolution ${metadata.width}x${metadata.height} exceeds max ${MAX_DIMENSION}px, resizing to PNG preserving aspect ratio...`);
        imageBuffer = await sharp(imageBuffer)
          .resize({ width: MAX_DIMENSION, height: MAX_DIMENSION, fit: sharp.fit.inside, withoutEnlargement: true })
          .png() // Ensure we output PNG
          .toBuffer();
        imageType = 'image/png';
        console.log(`Resized image resolution and converted to PNG`);
      }

      // Check if resizing is needed ONLY for file size > 25MB
      if (imageBuffer.byteLength > MAX_SIZE_BYTES) {
        console.log(`Image too large (${(imageBuffer.byteLength / (1024*1024)).toFixed(2)}MB), resizing preserving aspect ratio...`);
        try {
          imageBuffer = await sharp(imageBuffer)
            .resize(null, null, { // Resize based on fitting within limits, no fixed size
              fit: sharp.fit.inside, // Scale down to fit, preserve aspect ratio
              withoutEnlargement: true, // Don't upscale smaller images
            })
            .png() // Ensure output is PNG (one of the accepted types)
            .toBuffer();

          imageType = 'image/png'; // Type is now definitely PNG

          console.log(`Resized image size: ${(imageBuffer.byteLength / (1024*1024)).toFixed(2)}MB`);

          // FINAL check: Is the *resized* image still too large?
          if (imageBuffer.byteLength > MAX_SIZE_BYTES) {
            console.error(`Resized image still too large (${(imageBuffer.byteLength / (1024*1024)).toFixed(2)}MB)`);
            return NextResponse.json(
              {
                error: `Image too large even after resizing. Maximum size is ${MAX_SIZE_BYTES / (1024 * 1024)}MB.`,
              },
              { status: 400 }
            );
          }
        } catch (resizeError: any) {
            console.error("Error resizing image:", resizeError);
            return NextResponse.json(
                { error: "Failed to resize image", message: resizeError.message },
                { status: 500 }
            );
        }
      }

      // Use the potentially resized buffer for moderation
      const base64Image = imageBuffer.toString("base64");
      const dataURI = `data:${imageType};base64,${base64Image}`; // Use determined type

      // Perform moderation check if not skipped
      if (!shouldSkipModeration) {
        try {
          const moderationResult = await analyzeImage(dataURI);

          // Only reject if confidence is very low (below 0.3)
          if (
            !moderationResult.isDrawing &&
            moderationResult.confidence < 0.3
          ) {
            return NextResponse.json(
              {
                error: "Moderation failed",
                message: moderationResult.message,
                confidence: moderationResult.confidence,
              },
              { status: 400 }
            );
          }
        } catch (moderationError) {
          console.error("Error during image moderation:", moderationError);
          // Continue with the request even if moderation fails
        }
      }

      try {
        // Convert the resized or original buffer directly into a File-like Uploadable with the correct MIME type
        const openAIFile = await toFile(imageBuffer, 'upload.png', { type: imageType });
        const response = await openai.images.edit({
          model: "gpt-image-1",
          image: openAIFile,
          prompt: SYSTEM_PROMPT,
          size: "auto",
          quality: "auto",
        });

        // Check if the response data and b64_json exist
        const b64Json = response.data?.[0]?.b64_json;
        if (!b64Json) {
          throw new Error("Image generation failed: No b64_json returned");
        }

        // Return the generated image as a base64 data URI
        const imageUrl = `data:image/png;base64,${b64Json}`;

        return NextResponse.json({
          success: true,
          imageUrl: imageUrl, // Use the base64 data URI
        });
      } catch (openaiError: any) {
        console.error("OpenAI API error:", openaiError);

        // Format OpenAI errors properly
        if (openaiError instanceof OpenAI.APIError) {
          return NextResponse.json(
            {
              error: "OpenAI API error",
              message: openaiError.message,
              code: openaiError.code,
              type: openaiError.type,
            },
            { status: openaiError.status || 500 }
          );
        }

        // Handle any other OpenAI errors
        return NextResponse.json(
          {
            error: "Image generation failed",
            message: openaiError.message || "Error communicating with OpenAI",
          },
          { status: 500 }
        );
      }
    } catch (processingError) {
      console.error("Error processing image:", processingError);
      return NextResponse.json(
        {
          error: "Processing error",
          message: "Failed to process the uploaded image",
        },
        { status: 500 }
      );
    }
  } catch (requestError) {
    console.error("Request handling error:", requestError);
    return NextResponse.json(
      {
        error: "Request error",
        message: "Failed to process your request",
      },
      { status: 500 }
    );
  }
}
